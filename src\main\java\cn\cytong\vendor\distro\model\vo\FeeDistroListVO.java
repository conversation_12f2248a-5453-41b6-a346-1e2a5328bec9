package cn.cytong.vendor.distro.model.vo;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class FeeDistroListVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String distroId;
    private String ledgerId;
    private Date renewalTime;
    private Date settleTime;

    private String productId;
    private String productName;

    private String ticketId;
    private String ticketNumber;

    private String cardId;
    private String cardNumber;

    private String vendorId;
    private String vendorName;
    private String vendorLevel;

    private String feeRuleId;
    private String feeRuleName;

    private BigDecimal feeAmount;
    private String status;
    private String withdrawalRequestId;
    private Date createTime;
}
