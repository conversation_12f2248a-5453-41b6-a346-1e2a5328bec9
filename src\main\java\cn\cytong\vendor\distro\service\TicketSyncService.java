package cn.cytong.vendor.distro.service;

import cn.cytong.vendor.common.util.Tools;
import cn.cytong.vendor.distro.dao.*;
import cn.cytong.vendor.distro.enums.ServiceType;
import cn.cytong.vendor.distro.enums.TicketStautsEnum;
import cn.cytong.vendor.distro.model.dto.CardRenewalDTO;
import cn.cytong.vendor.distro.model.dto.TicketActivateDTO;
import cn.cytong.vendor.distro.model.dto.TicketAddDTO;
import cn.cytong.vendor.distro.model.dto.TicketUsedDTO;
import cn.cytong.vendor.distro.model.po.*;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.noear.solon.data.annotation.Transaction;
import org.sagacity.sqltoy.model.EntityQuery;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 实体券同步
 *
 * <AUTHOR> 2025/5/20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TicketSyncService {

    private final TicketDao ticketDao;
    private final ProductDao productDao;
    private final CardDao cardDao;
    private final FeeLedgerDao feeLedgerDao;
    private final VendorDao vendorDao;
    private final ServiceLogService logService;
    private final TicketVendorDao ticketVendorDao;
    private final RenewalDao renewalDao;
    private final FeeDistroDao feeDistroDao;

    /**
     * 新增实体券
     */
    @Transaction
    public void addTickets(TicketAddDTO dto) {
        log.info("新增实体券 {} 张, fistTicketNumber: {}, lastTicketNumber: {}", dto.getTicketNumbers().size(),
                dto.getTicketNumbers().get(0), dto.getTicketNumbers().get(dto.getTicketNumbers().size() - 1));

        Product product = productDao.fetchByGid(dto.getProductGid());
        Validator.validateTrue(product != null, "年卡信息异常 %s", dto.getProductGid());

        // 保存号段信息
        List<String> numberList = dto.getTicketNumbers();
        Validator.validateNotEmpty(numberList, "实体券信息不能为空");
        String max = numberList.stream().max(String::compareTo).get();
        String min = numberList.stream().min(String::compareTo).get();

        Validator.validateTrue(Tools.isConsecutive(numberList), "实体券号段不连续");

        // 判断ticketNumber是否已存在
        Long existCount = ticketDao.countByQuery(EntityQuery.create().where("ticket_number between ? and ?")
                .values(min, max));
        Validator.validateTrue(existCount <= 0, "存在已导入实体券 数量: %d", existCount);

        // 保存实体券信息
        List<Ticket> tickets = new ArrayList<>();
        for (String ticketNumber : numberList) {
            Ticket ticket = new Ticket();
            ticket.setTicketNumber(ticketNumber);
            ticket.setProductId(product.getProductId());
            ticket.setStatus(TicketStautsEnum.Available);
            ticket.setIsDeleted(false);
            ticket.setCreateTime(new Date());
            ticket.setUpdateTime(new Date());
            tickets.add(ticket);
        }

        // 保存日志
        ServiceLog log = ServiceLog.createSegmentLog(ServiceType.CreateTicket, product.getProductId(), null, min, max)
                .setTicketCount(tickets.size())
                .setDescription("新增实体券 " + tickets.size() + " 张");
        logService.saveLog(log);

        // 并行保存
        ticketDao.saveAll(tickets);
    }

    /**
     * 实体券激活
     */
    @Transaction
    public void activate(TicketActivateDTO dto) {
        log.info("实体券激活 {}", dto.getStartNumber());

        Product product = productDao.fetchByGid(dto.getProductGid());
        Validator.validateTrue(product != null, "年卡信息异常 %s", dto.getProductGid());

        // 查询实体券 并校验数量
        List<Ticket> tickets = ticketDao.queryByNum(dto.getStartNumber(), dto.getEndNumber());
        Validator.validateNotEmpty(tickets, "实体券不存在");

        // 激活
        tickets.forEach(ticket -> {
            if (ticket.getStatus() == TicketStautsEnum.Available) {
                log.info("实体券激活 {}", ticket.getTicketNumber());
                ticket.setStatus(TicketStautsEnum.Activated)
                        .setActivatedTime(new Date());
            }
        });
        ticketDao.updateAll(tickets);

        // 保存日志
        ServiceLog log = ServiceLog.createSegmentLog(ServiceType.Activate, product.getProductId(), null, dto.getStartNumber(), dto.getEndNumber())
                .setTicketCount(tickets.size())
                .setDescription(String.format("实体券激活 %s - %s", dto.getStartNumber(), dto.getEndNumber()));
        logService.saveLog(log);
    }

    /**
     * 实体券开卡(绑定用户信息)
     */
    @Transaction
    public void ticketUsed(TicketUsedDTO dto) {
        log.info("实体券开卡 {}", dto.getCardNumber());

        Ticket ticket = ticketDao.fetchByNum(dto.getTicketNumber());
        Validator.validateNotNull(ticket, "实体券不存在 %s", dto.getTicketNumber());

        Card card = cardDao.fetchByIdOrNumber(null, dto.getCardNumber());
        if (card != null) {
            log.info("卡号 {} 重复通知", dto.getCardNumber());
            return;
        }

        // 保存开卡信息
        card = new Card();
        card.setCardNumber(dto.getCardNumber());
        card.setUserInfo(dto.getUserInfo());
        card.setCustomerName(dto.getCustomerName());
        card.setIdCard(dto.getIdCard());
        card.setMobile(dto.getMobile());
        card.setTicketId(ticket.getTicketId());
        card.setTicketNumber(ticket.getTicketNumber());
        card.setVendorId(ticket.getVendorId());
        card.setProductId(ticket.getProductId());
        card.setActivationTime(new Date());
        card.setExpiryDate(DateUtil.parseDate(dto.getExpiryDate()));
        card.setStatus("Active");
        card.setIsDeleted(false);
        cardDao.saveOrUpdate(card);

        // 标记实体券已使用
        ticket.setStatus(TicketStautsEnum.Used)
                .setCardId(card.getCardId());
        ticketDao.update(ticket);

        // 保存日志
        logService.saveOpen(ticket, card, "用户开卡");
    }

    /**
     * 年卡续期
     */
    @Transaction
    public void cardRenew(CardRenewalDTO dto) {
        Card card = cardDao.fetchByIdOrNumber(null, dto.getCardNumber());
        Validator.validateNotNull(card, "年卡不存在 %s", dto.getCardNumber());

        // todo 判断 renewal_info 结构
        Long count = renewalDao.countByQuery(EntityQuery.create().where("card_id = ? and renewal_info = ?")
                .values(card.getCardId(), dto.getRenewalInfo()));
        if (count > 0) {
            // 判定重复请求
            return;
        }

        Date oldDate = card.getExpiryDate();

        // 保存续期信息
        Renewal renewal = new Renewal();
        renewal.setProductId(card.getProductId());
        renewal.setCardId(card.getCardId());
        renewal.setCardNumber(card.getCardNumber());
        renewal.setOpenTicketId(card.getTicketId());
        renewal.setOpenVendorId(ticketDao.fetchVendorId(card.getTicketId()));
        renewal.setRenewalInfo(dto.getRenewalInfo());
        renewal.setRenewalTime(new Date());
        Optional.ofNullable(dto.getAmount()).map(integer -> (double) integer).map(aDouble -> aDouble / 100)
                .map(BigDecimal::valueOf).ifPresent(renewal::setRenewalAmount);
        Optional.ofNullable(dto.getExpiryDate()).map(DateTime::new).map(DateTime::toJdkDate)
                .ifPresent(renewal::setNewExpiryDate);
        renewalDao.save(renewal);

        if (renewal.getNewExpiryDate() != null) {
            card.setExpiryDate(renewal.getNewExpiryDate());
            cardDao.update(card);
        }

        // 保存日志
        logService.saveRenew(renewal, String.format("续期 %s > %s",
                DateUtil.formatDate(oldDate), DateUtil.formatDate(card.getExpiryDate())));

        // todo 触发佣金计算
    }
}