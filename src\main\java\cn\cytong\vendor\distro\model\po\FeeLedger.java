package cn.cytong.vendor.distro.model.po;

import cn.cytong.vendor.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.sagacity.sqltoy.config.annotation.*;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 佣金总账对象 fee_ledger
 * 
 * <AUTHOR>
 * @since 2025-04-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Entity(tableName = "fee_ledger")
public class FeeLedger extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 账目唯一标识 (UUID) */
    @Id(strategy = "generator", generator = "org.sagacity.sqltoy.plugins.id.impl.SnowflakeIdGenerator")
    @Column(name = "ledger_id", comment = "账目唯一标识 (UUID)")
    private String ledgerId;

    /** 关联的续期记录ID (关联 renewal.renewal_id) */
    @Column(name = "renewal_id", comment = "关联的续期记录ID (关联 renewal.renewal_id)")
    private String renewalId;

    /** 佣金计算时间 */
    @Column(name = "calc_time", comment = "佣金计算时间")
    private Date calcTime;

    /** 关联的产品ID (关联 product.product_id) */
    @Column(name = "product_id", comment = "关联的产品ID (关联 product.product_id)")
    private String productId;

    /** 触发佣金的年卡ID (关联 card.card_id) */
    @Column(name = "card_id", comment = "触发佣金的年卡ID (关联 card.card_id)")
    private String cardId;

    /** 追溯到的原始激活券ID (关联 ticket.ticket_id) */
    @Column(name = "ticket_id", comment = "追溯到的原始激活券ID (关联 ticket.ticket_id)")
    private String ticketId;

    /** 本次计算使用的佣金规则集ID (关联 fee_rule_set.rule_set_id) */
    @Column(name = "fee_rule_set_id", comment = "本次计算使用的佣金规则集ID (关联 fee_rule_set.rule_set_id)")
    private String feeRuleSetId;

    /** 本次续期计算出的总佣金 */
    @Column(name = "total_fee", comment = "本次续期计算出的总佣金")
    private BigDecimal totalFee;

    /** 产品是否删除 (1:是, 0:否) */
    @Column(name = "is_deleted", comment = "产品是否删除 (1:是, 0:否)")
    private Boolean isDeleted;

    /* 关联对象 */
    @OneToOne(fields = "productId", mappedFields = "productId")
    private Product product;

    @OneToOne(fields = "cardId", mappedFields = "cardId")
    private Card card;

    @OneToOne(fields = "ticketId", mappedFields = "ticketId")
    private Ticket ticket;

    @OneToOne(fields = "feeRuleSetId", mappedFields = "ruleSetId")
    private FeeRuleSet feeRuleSet;

    @OneToMany(fields = "ledgerId", mappedFields = "ledgerId")
    private List<FeeDistro> distroList;
} 