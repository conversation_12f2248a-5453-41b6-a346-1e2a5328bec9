package cn.cytong.vendor.distro.dao;

import cn.cytong.vendor.common.base.BaseDao;
import cn.cytong.vendor.common.util.AuthUtils;
import cn.cytong.vendor.distro.model.po.Vendor;
import org.noear.solon.annotation.Component;
import org.sagacity.sqltoy.model.MapKit;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 分销商信息Dao
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Component
public class VendorDao extends BaseDao<Vendor> {

    public Vendor loginVendor() {
        String userId = AuthUtils.loginId();
        return finder().sql("select * from vendor where user_id = ?")
                .values(userId)
                .fetchEntity();
    }

    /**
     * 判断指定分销商是否是指定分销商的子分销商
     *
     * @param vendorId       分销商id
     * @param parentVendorId 上级分销商id
     */
    public boolean isChildOf(String vendorId, String parentVendorId) {
        return finder("""
                select count(1) from vendor where vendor_id = :vendorId and parent_vendor_id = :parentVendorId
                """)
                       .values(MapKit.startOf("vendorId", vendorId)
                               .endOf("parentVendorId", parentVendorId))
                       .fetchInteger() > 0;
    }

    public List<String> allVendorIds() {
        return finder("select vendor_id from vendor where is_deleted = 0").queryList(String.class);
    }

    /**
     * 查询指定分销商的所有子分销商ID（包括自己的id）
     */
    public List<String> queryVendorIdWithAllChild(String vendorId) {
        List<String> children = finder("select vendor_id from vendor where ancestor_ids like ?", vendorId).queryString();
        List<String> list = new ArrayList<>(children);
        list.add(vendorId);
        return list;
    }

    /**
     * 增加分销商总佣金
     * 
     * @param vendorId    分销商id
     * @param feeAmount   佣金金额
     */
    public void addVendorTotalFee(String vendorId, BigDecimal feeAmount) {
        finder("update vendor set total_fee_earned = total_fee_earned + ? where vendor_id = ?")
                .values(feeAmount, vendorId)
                .execute();
    }

    public String fetchNameById(String vendorId) {
        return finder("select name from vendor where vendor_id = ?", vendorId).fetchString();
    }
}