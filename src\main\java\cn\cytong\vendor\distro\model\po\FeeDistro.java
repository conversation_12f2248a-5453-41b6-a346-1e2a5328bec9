package cn.cytong.vendor.distro.model.po;

import cn.cytong.vendor.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.sagacity.sqltoy.config.annotation.Column;
import org.sagacity.sqltoy.config.annotation.Entity;
import org.sagacity.sqltoy.config.annotation.Id;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 佣金分配明细对象 fee_distro
 * 
 * <AUTHOR>
 * @since 2025-04-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Entity(tableName = "fee_distro")
public class FeeDistro extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 分配明细唯一标识 (UUID) */
    @Id(strategy = "generator", generator = "org.sagacity.sqltoy.plugins.id.impl.SnowflakeIdGenerator")
    @Column(name = "distro_id", comment = "分配明细唯一标识 (UUID)")
    private String distroId;

    /** 关联的总账目ID (关联 fee_ledger.ledger_id) */
    @Column(name = "ledger_id", comment = "关联的总账目ID (关联 fee_ledger.ledger_id)")
    private String ledgerId;

    /** 续期时间 */
    @Column(name = "renewal_id", comment = "续期时间")
    private String renewalId;

    /** 产品ID (关联 product.product_id) */
    @Column(name = "product_id", comment = "产品ID (关联 product.product_id)")
    private String productId;

    /** 票据ID (关联 ticket.ticket_id) */
    @Column(name = "ticket_id", comment = "票据ID (关联 ticket.ticket_id)")
    private String ticketId;

    /** 卡片ID (关联 card.card_id) */
    @Column(name = "card_id", comment = "卡片ID (关联 card.card_id)")
    private String cardId;

    /** 获得佣金的分销商ID (关联 vendor.vendor_id) */
    @Column(name = "vendor_id", comment = "获得佣金的分销商ID (关联 vendor.vendor_id)")
    private String vendorId;

    /** 分佣时该分销商的层级 */
    @Column(name = "vendor_level", comment = "分佣时该分销商的层级")
    private Integer vendorLevel;

    /** 佣金规则ID (关联 fee_rule.rule_id) */
    @Column(name = "fee_rule_id", comment = "佣金规则ID (关联 fee_rule.rule_id)")
    private String feeRuleId;

    /** 该分销商获得的佣金金额 */
    @Column(name = "fee_amount", comment = "该分销商获得的佣金金额")
    private BigDecimal feeAmount;

    /** 佣金状态 (PendingSettlement, Settled, IncludedInWithdrawal) */
    @Column(name = "status", comment = "佣金状态 (PendingSettlement, Settled, IncludedInWithdrawal)")
    private String status;

    /** 续期时间 (加入可提现余额时间) */
    @Column(name = "renewal_time", comment = "续期时间 (加入可提现余额时间)")
    private Date renewalTime;

    /** 结算时间 (加入可提现余额时间) */
    @Column(name = "settle_time", comment = "结算时间 (加入可提现余额时间)")
    private Date settleTime;

    /** 产品是否删除 (1:是, 0:否) */
    @Column(name = "is_deleted", comment = "产品是否删除 (1:是, 0:否)")
    private Boolean isDeleted;
} 