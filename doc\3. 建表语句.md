好的，这是根据您最终确认的需求和规范生成的 MySQL 建表语句。

**注意:**

*   所有表都包含 `create_by`, `update_by`, `create_time`, `update_time` 公共字段。
*   主键统一使用 `VARCHAR(36)` 类型（用于存储 UUID）。
*   不包含 `FOREIGN KEY` 约束，但通过注释标明了关联关系。
*   时间类型使用 `DATETIME`。
*   JSON 类型使用 `TEXT`。
*   索引根据常见查询场景建立，低基数的 `status` 等字段未加索引。
*   `DECIMAL` 类型默认使用 `(18, 4)`，您可以根据实际精度需求调整。

```sql
-- ----------------------------
-- Table structure for vendor (分销商表)
-- ----------------------------
DROP TABLE IF EXISTS `vendor`;
CREATE TABLE `vendor` (
  `vendor_id` varchar(36) NOT NULL COMMENT '分销商唯一标识 (UUID)',
  `user_id` varchar(36) NOT NULL COMMENT '关联的账户ID (来自用户账户表)',
  `name` varchar(255) NOT NULL COMMENT '分销商名称/公司名',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `phone_number` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(255) DEFAULT NULL COMMENT '电子邮箱',
  `level` int NOT NULL COMMENT '分销商层级 (1, 2, ..., N)',
  `parent_vendor_id` varchar(36) DEFAULT NULL COMMENT '上级分销商ID (关联 vendor.vendor_id)',
  `role_id` varchar(36) NOT NULL COMMENT '关联的角色ID (来自角色表)',
  `available_fee_balance` decimal(18,4) NOT NULL DEFAULT '0.0000' COMMENT '当前可提现佣金余额',
  `total_fee_earned` decimal(18,4) NOT NULL DEFAULT '0.0000' COMMENT '累计赚取佣金',
  `total_fee_withdrawn` decimal(18,4) NOT NULL DEFAULT '0.0000' COMMENT '累计已提现佣金',
  `bank_account_info` text COMMENT '银行账户信息 (JSON格式, 可能加密)',
  `status` varchar(20) NOT NULL DEFAULT 'Active' COMMENT '分销商状态 (Active, Inactive, Suspended)',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`vendor_id`),
  UNIQUE KEY `uk_vendor_user_id` (`user_id`),
  KEY `idx_vendor_parent_id` (`parent_vendor_id`),
  KEY `idx_vendor_role_id` (`role_id`),
  KEY `idx_vendor_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销商信息表';

-- ----------------------------
-- Table structure for product (年卡产品表)
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
  `product_id` varchar(36) NOT NULL COMMENT '产品唯一标识 (UUID)',
  `product_name` varchar(255) NOT NULL COMMENT '年卡名称',
  `description` text COMMENT '产品描述',
  `fee_rule_set_id` varchar(36) DEFAULT NULL COMMENT '关联的特定佣金规则集ID (关联 fee_rule_set.rule_set_id)',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '产品是否启用 (1:是, 0:否)',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`product_id`),
  KEY `idx_product_fee_rule_set_id` (`fee_rule_set_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='年卡产品表';

-- ----------------------------
-- Table structure for fee_rule_set (佣金规则集表)
-- ----------------------------
DROP TABLE IF EXISTS `fee_rule_set`;
CREATE TABLE `fee_rule_set` (
  `rule_set_id` varchar(36) NOT NULL COMMENT '规则集唯一标识 (UUID)',
  `rule_set_name` varchar(255) NOT NULL COMMENT '规则集名称',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为系统全局默认规则 (1:是, 0:否)',
  `description` text COMMENT '规则集描述',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`rule_set_id`),
  UNIQUE KEY `uk_fee_rule_set_name` (`rule_set_name`),
  KEY `idx_fee_rule_set_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='佣金规则集表';

-- ----------------------------
-- Table structure for fee_rule_level (佣金规则层级明细表)
-- ----------------------------
DROP TABLE IF EXISTS `fee_rule_level`;
CREATE TABLE `fee_rule_level` (
  `rule_level_id` varchar(36) NOT NULL COMMENT '规则层级明细唯一标识 (UUID)',
  `fee_rule_set_id` varchar(36) NOT NULL COMMENT '所属规则集ID (关联 fee_rule_set.rule_set_id)',
  `level` int NOT NULL COMMENT '对应的分销商层级 (1, 2, ..., N)',
  `fee_type` varchar(20) NOT NULL COMMENT '佣金类型 (Percentage, FixedAmount)',
  `fee_value` decimal(18,4) NOT NULL COMMENT '佣金值 (百分比或金额)',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`rule_level_id`),
  UNIQUE KEY `uk_fee_rule_level_set_level` (`fee_rule_set_id`, `level`),
  KEY `idx_fee_rule_level_set_id` (`fee_rule_set_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='佣金规则层级明细表';

-- ----------------------------
-- Table structure for ticket_segment (实体券号段表)
-- ----------------------------
DROP TABLE IF EXISTS `ticket_segment`;
CREATE TABLE `ticket_segment` (
  `segment_id` varchar(36) NOT NULL COMMENT '号段唯一标识 (UUID)',
  `product_id` varchar(36) NOT NULL COMMENT '关联的产品ID (关联 product.product_id)',
  `start_number` varchar(100) NOT NULL COMMENT '号段起始券号',
  `end_number` varchar(100) NOT NULL COMMENT '号段结束券号',
  `total_count` int NOT NULL COMMENT '号段券总数',
  `assigned_to_vendor_id` varchar(36) DEFAULT NULL COMMENT '当前持有该号段的分销商ID (关联 vendor.vendor_id, NULL表示在管理员)',
  `parent_segment_id` varchar(36) DEFAULT NULL COMMENT '父号段ID (关联 ticket_segment.segment_id)',
  `assign_time` datetime DEFAULT NULL COMMENT '分配时间',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '号段是否可用 (1:是, 0:否)',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`segment_id`),
  KEY `idx_ticket_segment_product_id` (`product_id`),
  KEY `idx_ticket_segment_assigned_vendor` (`assigned_to_vendor_id`),
  KEY `idx_ticket_segment_parent_id` (`parent_segment_id`),
  KEY `idx_ticket_segment_start_num` (`start_number`),
  KEY `idx_ticket_segment_end_num` (`end_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实体券号段表';

-- ----------------------------
-- Table structure for ticket (实体券表)
-- ----------------------------
DROP TABLE IF EXISTS `ticket`;
CREATE TABLE `ticket` (
  `ticket_id` varchar(36) NOT NULL COMMENT '实体券唯一标识 (UUID)',
  `ticket_number` varchar(100) NOT NULL COMMENT '实体券券号',
  `product_id` varchar(36) NOT NULL COMMENT '关联的产品ID (关联 product.product_id)',
  `current_segment_id` varchar(36) NOT NULL COMMENT '当前所属号段ID (关联 ticket_segment.segment_id)',
  `original_vendor_chain` text COMMENT '原始分销商链ID (JSON数组格式: [vid1, vid2, vid3])',
  `status` varchar(20) NOT NULL DEFAULT 'Available' COMMENT '券状态 (Available, Activated, Used)',
  `activated_time` datetime DEFAULT NULL COMMENT '激活时间 (由第三方同步)',
  `card_id` varchar(36) DEFAULT NULL COMMENT '关联的激活后的年卡ID (关联 card.card_id)',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`ticket_id`),
  UNIQUE KEY `uk_ticket_number` (`ticket_number`),
  KEY `idx_ticket_product_id` (`product_id`),
  KEY `idx_ticket_current_segment_id` (`current_segment_id`),
  KEY `idx_ticket_card_id` (`card_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实体券表';

-- ----------------------------
-- Table structure for card (年卡信息表)
-- ----------------------------
DROP TABLE IF EXISTS `card`;
CREATE TABLE `card` (
  `card_id` varchar(36) NOT NULL COMMENT '系统内年卡唯一标识 (UUID)',
  `card_number` varchar(100) NOT NULL COMMENT '第三方系统年卡卡号',
  `third_party_user_id` varchar(100) DEFAULT NULL COMMENT '第三方系统的用户标识',
  `user_info` text COMMENT '同步过来的用户信息 (JSON格式)',
  `ticket_id` varchar(36) NOT NULL COMMENT '用于激活此卡的实体券ID (关联 ticket.ticket_id)',
  `activation_time` datetime NOT NULL COMMENT '激活时间',
  `expiry_date` date DEFAULT NULL COMMENT '年卡到期日期',
  `status` varchar(20) NOT NULL DEFAULT 'Active' COMMENT '年卡状态 (Active, Expired, Cancelled)',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID (记录同步操作者)',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间 (同步时间)',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间 (最后同步时间)',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`card_id`),
  UNIQUE KEY `uk_card_number` (`card_number`),
  UNIQUE KEY `uk_card_ticket_id` (`ticket_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='年卡信息表 (由第三方同步)';

-- ----------------------------
-- Table structure for renewal (续期记录表)
-- ----------------------------
DROP TABLE IF EXISTS `renewal`;
CREATE TABLE `renewal` (
  `renewal_id` varchar(36) NOT NULL COMMENT '续期记录唯一标识 (UUID)',
  `card_id` varchar(36) NOT NULL COMMENT '关联的年卡ID (关联 card.card_id)',
  `renewal_time` datetime NOT NULL COMMENT '续期发生时间',
  `renewal_amount` decimal(18,4) DEFAULT NULL COMMENT '续期支付金额',
  `product_id` varchar(36) DEFAULT NULL COMMENT '本次续期对应的产品ID (关联 product.product_id)',
  `fee_processed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '佣金是否已计算处理 (1:是, 0:否)',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID (记录同步操作者)',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间 (同步时间)',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`renewal_id`),
  KEY `idx_renewal_card_id` (`card_id`),
  KEY `idx_renewal_time` (`renewal_time`),
  KEY `idx_renewal_fee_processed` (`fee_processed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='续期记录表 (由第三方同步)';

-- ----------------------------
-- Table structure for fee_ledger (佣金总账表)
-- ----------------------------
DROP TABLE IF EXISTS `fee_ledger`;
CREATE TABLE `fee_ledger` (
  `ledger_id` varchar(36) NOT NULL COMMENT '账目唯一标识 (UUID)',
  `renewal_id` varchar(36) NOT NULL COMMENT '关联的续期记录ID (关联 renewal.renewal_id)',
  `calc_time` datetime NOT NULL COMMENT '佣金计算时间',
  `card_id` varchar(36) NOT NULL COMMENT '触发佣金的年卡ID (关联 card.card_id)',
  `ticket_id` varchar(36) NOT NULL COMMENT '追溯到的原始激活券ID (关联 ticket.ticket_id)',
  `fee_rule_set_id` varchar(36) NOT NULL COMMENT '本次计算使用的佣金规则集ID (关联 fee_rule_set.rule_set_id)',
  `total_fee` decimal(18,4) NOT NULL COMMENT '本次续期计算出的总佣金',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID (系统自动处理时可为特定标识)',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`ledger_id`),
  UNIQUE KEY `uk_fee_ledger_renewal_id` (`renewal_id`), -- 一个续期只生成一条总账
  KEY `idx_fee_ledger_card_id` (`card_id`),
  KEY `idx_fee_ledger_ticket_id` (`ticket_id`),
  KEY `idx_fee_ledger_rule_set_id` (`fee_rule_set_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='佣金总账表';

-- ----------------------------
-- Table structure for fee_distro (佣金分配明细表)
-- ----------------------------
DROP TABLE IF EXISTS `fee_distro`;
CREATE TABLE `fee_distro` (
  `distro_id` varchar(36) NOT NULL COMMENT '分配明细唯一标识 (UUID)',
  `ledger_id` varchar(36) NOT NULL COMMENT '关联的总账目ID (关联 fee_ledger.ledger_id)',
  `vendor_id` varchar(36) NOT NULL COMMENT '获得佣金的分销商ID (关联 vendor.vendor_id)',
  `vendor_level` int NOT NULL COMMENT '分佣时该分销商的层级',
  `fee_amount` decimal(18,4) NOT NULL COMMENT '该分销商获得的佣金金额',
  `status` varchar(30) NOT NULL DEFAULT 'PendingSettlement' COMMENT '佣金状态 (PendingSettlement, Settled, IncludedInWithdrawal)',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间 (加入可提现余额时间)',
  `withdrawal_request_id` varchar(36) DEFAULT NULL COMMENT '关联的提现申请ID (关联 withdrawal_request.request_id)',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`distro_id`),
  KEY `idx_fee_distro_ledger_id` (`ledger_id`),
  KEY `idx_fee_distro_vendor_id` (`vendor_id`),
  KEY `idx_fee_distro_withdrawal_id` (`withdrawal_request_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='佣金分配明细表';

-- ----------------------------
-- Table structure for withdrawal_request (提现申请表)
-- ----------------------------
DROP TABLE IF EXISTS `withdrawal_request`;
CREATE TABLE `withdrawal_request` (
  `request_id` varchar(36) NOT NULL COMMENT '提现申请唯一标识 (UUID)',
  `vendor_id` varchar(36) NOT NULL COMMENT '申请提现的分销商ID (关联 vendor.vendor_id)',
  `requested_amount` decimal(18,4) NOT NULL COMMENT '申请提现金额',
  `request_timestamp` datetime NOT NULL COMMENT '申请提交时间',
  `status` varchar(20) NOT NULL DEFAULT 'Pending' COMMENT '申请状态 (Pending, Approved, Rejected, Processing, Paid)',
  `bank_account_snapshot` text COMMENT '提现时使用的银行账户信息快照 (JSON格式, 可能加密)',
  `processed_by_user_id` varchar(36) DEFAULT NULL COMMENT '处理该申请的管理员账户ID (关联用户账户表)',
  `processing_timestamp` datetime DEFAULT NULL COMMENT '开始处理/审批时间',
  `completion_timestamp` datetime DEFAULT NULL COMMENT '打款完成/拒绝时间',
  `transaction_reference` varchar(255) DEFAULT NULL COMMENT '银行转账流水号 (打款凭证)',
  `rejection_reason` text COMMENT '拒绝理由',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID (即申请者vendor关联的user_id)',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID (通常是处理者user_id)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`request_id`),
  KEY `idx_withdrawal_request_vendor_id` (`vendor_id`),
  KEY `idx_withdrawal_request_timestamp` (`request_timestamp`),
  KEY `idx_withdrawal_request_processor` (`processed_by_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现申请表';

-- ----------------------------
-- Table structure for system_setting (系统设置表)
-- ----------------------------
DROP TABLE IF EXISTS `system_setting`;
CREATE TABLE `system_setting` (
  `setting_key` varchar(100) NOT NULL COMMENT '设置项键名',
  `setting_value` text COMMENT '设置项的值',
  `description` text COMMENT '描述',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `update_by` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标识 (0:未删除, 1:已删除)',
  PRIMARY KEY (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- ----------------------------
-- Example System Settings Data (Optional)
-- ----------------------------
-- INSERT INTO `