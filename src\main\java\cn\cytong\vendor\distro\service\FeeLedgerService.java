package cn.cytong.vendor.distro.service;

import cn.cytong.vendor.distro.dao.CardDao;
import cn.cytong.vendor.distro.dao.FeeLedgerDao;
import cn.cytong.vendor.distro.dao.VendorDao;
import cn.cytong.vendor.distro.model.dto.FeeLedgerDetailQueryDTO;
import cn.cytong.vendor.distro.model.dto.FeeLedgerListQueryDTO;
import cn.cytong.vendor.distro.model.po.FeeLedger;
import cn.cytong.vendor.distro.model.vo.FeeDistroListVO;
import cn.cytong.vendor.distro.model.vo.FeeLedgerDetailVO;
import cn.cytong.vendor.distro.model.vo.FeeLedgerSummaryVO;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.sagacity.sqltoy.model.Page;

import java.util.List;

import static java.util.Optional.ofNullable;

/**
 * 佣金查询
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FeeLedgerService {

    private final FeeLedgerDao dao;
    private final CardDao cardDao;
    private final VendorPermService permService;
    private final VendorDao vendorDao;

    /**
     * 分页查询分销商列表
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    public Page<FeeLedgerSummaryVO> queryPage(FeeLedgerListQueryDTO dto) {
        String cardId = null;
        if (dto.getCardNumber() != null) {
            cardId = cardDao.fetchIdByNumber(dto.getCardNumber());
        }

        List<String> limitVendors = permService.getVendorIdWhereIn();
        List<String> queryVendors = null;
        if (dto.getVendorId() != null) {
            queryVendors = vendorDao.queryVendorIdWithAllChild(dto.getVendorId());
        }

        Dict params = Dict.parse(dto)
                .set("limitVendors", limitVendors)
                .set("queryVendors", queryVendors)
                .set("cardId", cardId);

        return dao.finder("""
                        select f.*,
                               p.product_name,
                               c.card_number,
                               t.ticket_number,
                               frs.rule_set_name as fee_rule_set_name
                        from fee_ledger f
                        left join product p on f.product_id = p.product_id
                        left join card c on f.card_id = c.card_id
                        left join ticket t on f.ticket_id = t.ticket_id
                        left join fee_rule_set frs on f.fee_rule_set_id = frs.rule_set_id
                        where f.is_deleted = 0
                        #[AND f.calc_time >= :calcTimeStart]
                        #[AND f.calc_time < :calcTimeEnd]
                        #[AND f.product_id = :productId]
                        #[AND f.fee_rule_set_id = :feeRuleSetId]
                        #[AND f.total_fee >= :totalFeeStart]
                        #[AND f.total_fee < :totalFeeEnd]
                        #[AND f.card_id = :cardId]
                        #[AND f.ticket_id in (select distinct ticket_id from ticket where vendor_id in (:queryVendors))]
                        #[AND f.ticket_id in (select distinct ticket_id from ticket where vendor_id in (:limitVendors))]
                        #[AND f.status = :status]
                        #[order by @value(:sort) @value(:order)]
                        """)
                .values(params)
                .resultType(FeeLedgerSummaryVO.class)
                .page(dto.getPageNo(), dto.getPageSize())
                .queryPage();
    }

    public FeeLedgerDetailVO getDetail(FeeLedgerDetailQueryDTO dto) {
        FeeLedger feeLedger = dao.fetchById(dto.getLedgerId());
        if (feeLedger == null) {
            return null;
        }
        dao.loadCascade(feeLedger);
        FeeLedgerDetailVO vo = Convert.convert(FeeLedgerDetailVO.class, feeLedger);

        // 设置分佣明细的相关字段
        List<FeeDistroListVO> distroList = vo.getDistroList();
        for (FeeDistroListVO distroVO : distroList) {
            ofNullable(vo.getProduct()).ifPresent(p -> distroVO.setProductName(p.getProductName()));
            ofNullable(vo.getCard()).ifPresent(c -> distroVO.setCardNumber(c.getCardNumber()));
            ofNullable(vo.getTicket()).ifPresent(t -> distroVO.setTicketNumber(t.getTicketNumber()));
            ofNullable(vo.getFeeRuleSet()).ifPresent(f -> distroVO.setFeeRuleName(f.getRuleSetName()));
            ofNullable(distroVO.getVendorId()).ifPresent(id -> distroVO.setVendorName(vendorDao.fetchNameById(id)));
        }
        return vo;
    }
}