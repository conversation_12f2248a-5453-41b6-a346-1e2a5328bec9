-- 修改 fee_ledger 表，添加新字段
ALTER TABLE fee_ledger
    ADD COLUMN product_id VARCHAR(32) COMMENT '产品ID (关联 product.product_id)' AFTER ticket_id,
    ADD COLUMN product_name VARCHAR(100) COMMENT '产品名称' AFTER product_id,
    ADD COLUMN card_number VARCHAR(50) COMMENT '卡片编号' AFTER card_id,
    ADD COLUMN ticket_number VARCHAR(50) COMMENT '票据编号' AFTER ticket_id,
    ADD COLUMN fee_rule_set_name VARCHAR(100) COMMENT '使用的规则集名称' AFTER fee_rule_set_id;

-- 添加索引
ALTER TABLE fee_ledger
    ADD INDEX idx_product_id (product_id),
    ADD INDEX idx_card_number (card_number),
    ADD INDEX idx_ticket_number (ticket_number),
    ADD INDEX idx_fee_rule_set_id (fee_rule_set_id);

-- 更新现有数据
UPDATE fee_ledger l
LEFT JOIN card c ON l.card_id = c.card_id
SET l.card_number = c.card_number;

UPDATE fee_ledger l
LEFT JOIN ticket t ON l.ticket_id = t.ticket_id
SET l.ticket_number = t.ticket_number;

UPDATE fee_ledger l
LEFT JOIN fee_rule_set r ON l.fee_rule_set_id = r.rule_set_id
SET l.fee_rule_set_name = r.rule_set_name;

UPDATE fee_ledger l
LEFT JOIN renewal r ON l.renewal_id = r.renewal_id
LEFT JOIN product p ON r.product_id = p.product_id
SET l.product_id = p.product_id,
    l.product_name = p.product_name; 