package cn.cytong.vendor.distro.controller;

import cn.cytong.vendor.common.base.BaseController;
import cn.cytong.vendor.common.model.ApiPage;
import cn.cytong.vendor.common.model.ApiResult;
import cn.cytong.vendor.distro.model.dto.MyFeeDistroListQueryDTO;
import cn.cytong.vendor.distro.model.po.Vendor;
import cn.cytong.vendor.distro.model.vo.FeeDistroListVO;
import cn.cytong.vendor.distro.service.FeeDistroService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Controller;
import org.noear.solon.annotation.Get;
import org.noear.solon.annotation.Inject;
import org.noear.solon.annotation.Mapping;
import org.noear.solon.validation.annotation.Validated;
import org.sagacity.sqltoy.model.Page;

/**
 * 佣金分配查询
 */
@Slf4j
@Controller
public class FeeDistroController extends BaseController {

    @Inject
    private FeeDistroService feeDistroService;

    /**
     * 查询佣金分配列表
     */
    @Get
    @Mapping("/distro/fee-distro/my-list")
    @SaCheckPermission("distro:fee-distro:list")
    public ApiResult<ApiPage<FeeDistroListVO>> queryPage(@Validated MyFeeDistroListQueryDTO dto) {
        if (dto == null) {
            dto = new MyFeeDistroListQueryDTO();
        }
        dto = initPageAndSort(Vendor.class, dto);

        Page<FeeDistroListVO> pager = feeDistroService.queryPage(dto);
        return ApiResult.ok(ApiPage.rest(pager));
    }
}