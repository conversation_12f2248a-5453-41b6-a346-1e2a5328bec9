package cn.cytong.vendor.distro.service;

import cn.cytong.vendor.distro.dao.*;
import cn.cytong.vendor.distro.model.po.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.noear.solon.data.annotation.Transaction;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 续期佣金计算服务
 *
 * <AUTHOR>
 * @since 2025/6/1
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RenewalFeeService {
    private final RenewalDao renewalDao;
    private final CardDao cardDao;
    private final TicketVendorDao ticketVendorDao;
    private final VendorDao vendorDao;
    private final FeeRuleSetDao feeRuleSetDao;
    private final FeeLedgerDao feeLedgerDao;
    private final FeeDistroDao feeDistroDao;

    /**
     * 计算未处理的续期佣金
     */
    @Transaction
    public void calculatePendingRenewalFees() {
        // 1. 获取未处理佣金的续期记录
        List<Renewal> pendingRenewals = renewalDao.findPendingRenewals();
        if (CollUtil.isEmpty(pendingRenewals)) {
            return;
        }

        log.info("找到{}条未处理的续期佣金记录", pendingRenewals.size());

        // 2. 逐个处理续期记录
        int successCount = 0;
        int failCount = 0;
        for (Renewal renewal : pendingRenewals) {
            try {
                calculateSingleRenewalFee(renewal);
                successCount++;
                log.info("续期记录处理完成: {}", renewal.getRenewalId());
            } catch (Exception e) {
                failCount++;
                log.error("处理续期记录失败: {}, 原因: {}", renewal.getRenewalId(), e.getMessage(), e);
            }
        }

        log.info("续期佣金计算完成，成功: {}条，失败: {}条", successCount, failCount);
    }

    /**
     * 计算单条续期记录的佣金
     */
    @Transaction
    public void calculateSingleRenewalFee(Renewal renewal) {
        Validator.validateNotNull(renewal, "续期记录不能为空");

        if (renewal.getFeeProcessed()) {
            log.info("续期记录佣金已处理，跳过计算: {}", renewal.getRenewalId());
            return;
        }

        try {
            // 1. 获取年卡信息
            Card card = cardDao.fetchById(renewal.getCardId());
            Validator.validateNotNull(card, "年卡信息不存在: {}", renewal.getCardId());
            cardDao.loadCascade(card);

            // 2. 获取产品信息和佣金规则
            Product product = card.getProduct();
            Validator.validateNotNull(product, "产品信息不存在: {}", renewal.getProductId());

            Validator.validateNotNull(product.getFeeRuleSetId(), "产品分佣规则不存在: {}", product.getProductId());
            FeeRuleSet feeRuleSet = feeRuleSetDao.fetchById(product.getFeeRuleSetId());
            Validator.validateNotNull(feeRuleSet, "佣金规则不存在，产品ID: {}", product.getProductId());

            // 3. 获取原始激活券信息
            Ticket ticket = card.getTicket();
            Validator.validateNotNull(ticket, "激活券信息不存在: {}", card.getTicketId());

            // 4. 获取激活券的分销商关系链
            List<TicketVendor> ticketVendors = ticketVendorDao.findByTicketIdOrderByLevel(ticket.getTicketId());
            if (CollUtil.isEmpty(ticketVendors)) {
                renewal.setFeeProcessed(true);
                renewalDao.update(renewal);
                log.info("没有分销商关系链，激活券ID: {}", ticket.getTicketId());
                return;
            }

            // 5. 创建佣金台账
            FeeLedger feeLedger = new FeeLedger()
                    .setRenewalId(renewal.getRenewalId())
                    .setCalcTime(new Date())
                    .setProductId(product.getProductId())
                    .setCardId(card.getCardId())
                    .setTicketId(ticket.getTicketId())
                    .setFeeRuleSetId(feeRuleSet.getRuleSetId())
                    .setTotalFee(BigDecimal.ZERO);
            feeLedgerDao.save(feeLedger);

            // 6. 计算各级分销商佣金并创建分配记录
            List<FeeDistro> feeDistros = new ArrayList<>();
            BigDecimal totalFee = BigDecimal.ZERO;

            Date now = new Date();
            for (TicketVendor ticketVendor : ticketVendors) {
                // 根据分销商级别获取佣金比例
                BigDecimal feeRate = getFeeRateByLevel(feeRuleSet, ticketVendor.getVendorLevel());
                if (feeRate == null || feeRate.compareTo(BigDecimal.ZERO) <= 0) {
                    log.debug("跳过分销商佣金计算，分销商: {}，级别: {}，佣金比例为0", ticketVendor.getVendorId(), ticketVendor.getVendorLevel());
                    continue;
                }

                // 计算该级别分销商的佣金
                BigDecimal feeAmount;
                if ("Percentage".equals(feeRuleSet.getFeeType())) {
                    // 百分比类型，用续期金额乘以费率
                    feeAmount = renewal.getRenewalAmount().multiply(feeRate.divide(new BigDecimal("100")));
                    log.debug("计算百分比佣金: 续期金额={}, 费率={}%, 佣金={}",
                            renewal.getRenewalAmount(), feeRate, feeAmount);
                } else if ("FixedAmount".equals(feeRuleSet.getFeeType())) {
                    // 固定金额类型，直接使用费率值作为佣金
                    feeAmount = feeRate;
                    log.debug("使用固定金额佣金: {}", feeAmount);
                } else {
                    throw new IllegalArgumentException("不支持的佣金类型: " + feeRuleSet.getFeeType());
                }

                totalFee = totalFee.add(feeAmount);

                // 创建分配记录
                FeeDistro feeDistro = new FeeDistro()
                        .setLedgerId(feeLedger.getLedgerId())
                        .setVendorId(ticketVendor.getVendorId()).setVendorLevel(ticketVendor.getVendorLevel())
                        .setProductId(product.getProductId())
                        .setCardId(card.getCardId())
                        .setTicketId(ticket.getTicketId())
                        .setRenewalId(renewal.getRenewalId()).setRenewalTime(renewal.getRenewalTime())
                        .setFeeRuleId(feeRuleSet.getRuleSetId())
                        .setFeeAmount(feeAmount)
                        .setSettleTime(now)
                        .setStatus("PendingSettlement");

                feeDistros.add(feeDistro);
                log.debug("计算分销商佣金: 分销商={}, 级别={}, 金额={}", ticketVendor.getVendorId(), ticketVendor.getVendorLevel(), feeAmount);

                // 增加分销商总佣金
                vendorDao.addVendorTotalFee(ticketVendor.getVendorId(), feeAmount);
            }

            // 7. 更新总账金额并保存分配记录
            feeLedger.setTotalFee(totalFee);
            feeLedgerDao.update(feeLedger);
            log.info("更新佣金台账总金额: {}", totalFee);

            if (CollUtil.isNotEmpty(feeDistros)) {
                feeDistroDao.saveAll(feeDistros);
                log.info("保存{}条佣金分配记录", feeDistros.size());
            }

            // 8. 更新续期记录状态为已处理
            renewal.setFeeProcessed(true);
            renewalDao.update(renewal);
            log.info("更新续期记录状态为已处理: {}", renewal.getRenewalId());

        } catch (Exception e) {
            log.error("计算续期佣金失败: {}", renewal.getRenewalId(), e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }

    /**
     * 根据分销商级别获取佣金比例或固定金额
     *
     * @param feeRuleSet 佣金规则集
     * @param level      分销商级别
     * @return 如果是百分比类型返回百分比值(例如25表示25 %)，如果是固定金额类型返回具体金额
     */
    private BigDecimal getFeeRateByLevel(FeeRuleSet feeRuleSet, Integer level) {
        Validator.validateNotNull(feeRuleSet, "佣金规则不能为空");
        Validator.validateNotNull(level, "分销商级别不能为空");
        Validator.validateNotEmpty(feeRuleSet.getFeeType(), "佣金类型不能为空");

        if (!("Percentage".equals(feeRuleSet.getFeeType()) || "FixedAmount".equals(feeRuleSet.getFeeType()))) {
            throw new IllegalArgumentException("不支持的佣金类型: " + feeRuleSet.getFeeType());
        }

        return switch (level) {
            case 1 -> feeRuleSet.getFeeValue1();
            case 2 -> feeRuleSet.getFeeValue2();
            case 3 -> feeRuleSet.getFeeValue3();
            case 4 -> feeRuleSet.getFeeValue4();
            default -> throw new IllegalArgumentException("不支持的分销商级别: " + level);
        };
    }

}
