package cn.cytong.vendor.distro.service;

import cn.cytong.vendor.common.util.AuthUtils;
import cn.cytong.vendor.distro.dao.FeeDistroDao;
import cn.cytong.vendor.distro.dao.VendorDao;
import cn.cytong.vendor.distro.model.dto.MyFeeDistroListQueryDTO;
import cn.cytong.vendor.distro.model.po.FeeDistro;
import cn.cytong.vendor.distro.model.po.Vendor;
import cn.cytong.vendor.distro.model.vo.FeeDistroListVO;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.sagacity.sqltoy.model.Page;

import java.util.Optional;

/**
 * 佣金查询
 */
@Slf4j
@Component
public class FeeDistroService {

    @Inject
    FeeDistroDao dao;
    @Inject
    VendorDao vendorDao;

    /**
     * 分页查询分销商列表
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    public Page<FeeDistroListVO> queryPage(MyFeeDistroListQueryDTO dto) {
        String vendorId = null;
        if (!AuthUtils.isRoot()) {
            Vendor vendor = vendorDao.loginVendor();
            vendorId = Optional.ofNullable(vendor).map(Vendor::getVendorId).orElse("0");
        }
        return dao.finder("""
                select * from fee_distro
                where is_deleted = 0
                #[AND settle_time >= :settlementTimeStart]
                #[AND settle_time < :settlementTimeEnd]
                #[AND level = :level]
                #[AND status = :status]
                #[AND vendor_id = :vendorId]
                #[order by @value(:sort) @value(:order)]
                """)
                .values(dto, new FeeDistro().setVendorId(vendorId))
                .resultType(FeeDistroListVO.class)
                .page(dto.getPageNo(), dto.getPageSize())
                .queryPage();
    }
}