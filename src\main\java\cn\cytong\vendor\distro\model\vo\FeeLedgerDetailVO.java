package cn.cytong.vendor.distro.model.vo;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class FeeLedgerDetailVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String ledgerId;
    private String renewalId;
    private Date calcTime;    
    private String cardId;    
    private String productId;
    private String ticketId;
    private String feeRuleSetId;
    private BigDecimal totalFee;
    private Boolean isDeleted;

    private ProductListVO product;
    private CardListVO card;
    private TicketListVO ticket;
    private FeeRuleSetDetailVO feeRuleSet;
    private List<FeeDistroListVO> distroList;
}
